import 'package:equatable/equatable.dart';
import 'package:quran_flutter/features/quran/domain/entities/ayah.dart';
import 'package:quran_flutter/features/quran/domain/entities/surah.dart';

abstract class QuranState extends Equatable {
  const QuranState();

  @override
  List<Object?> get props => [];
}

class Quran<PERSON><PERSON>tial extends QuranState {}

class QuranLoading extends QuranState {}

class QuranLoaded extends QuranState {
  final List<Surah> surahs;
  final List<Ayah>? ayahs;
  final String? currentView;

  const QuranLoaded({
    required this.surahs,
    this.ayahs,
    this.currentView,
  });

  @override
  List<Object?> get props => [surahs, ayahs, currentView];
}

class QuranError extends QuranState {
  final String message;

  const QuranError(this.message);

  @override
  List<Object?> get props => [message];
} 