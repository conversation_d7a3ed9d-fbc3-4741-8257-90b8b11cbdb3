import 'dart:convert';
import 'package:flutter/services.dart';
import 'package:quran_flutter/features/adhkar/data/models/dhikr_model.dart';

abstract class AdhkarLocalDataSource {
  Future<List<DhikrModel>> getMorningAdhkar();
  Future<List<DhikrModel>> getEveningAdhkar();
  Future<List<DhikrModel>> getAfterPrayerAdhkar();
  Future<List<DhikrModel>> getBeforeSleepAdhkar();
  Future<List<DhikrModel>> getAdhkarByCategory(String category);
  Future<List<DhikrModel>> searchAdhkar(String query);
}

class AdhkarLocalDataSourceImpl implements AdhkarLocalDataSource {
  @override
  Future<List<DhikrModel>> getMorningAdhkar() async {
    try {
      final String response = await rootBundle.loadString('assets/data/adhkar.json');
      final List<dynamic> data = json.decode(response);
      return data
          .where((dhikr) => dhikr['category'] == 'morning')
          .map((json) => DhikrModel.fromJson(json))
          .toList();
    } catch (e) {
      throw Exception('Failed to load Morning Adhkar: $e');
    }
  }

  @override
  Future<List<DhikrModel>> getEveningAdhkar() async {
    try {
      final String response = await rootBundle.loadString('assets/data/adhkar.json');
      final List<dynamic> data = json.decode(response);
      return data
          .where((dhikr) => dhikr['category'] == 'evening')
          .map((json) => DhikrModel.fromJson(json))
          .toList();
    } catch (e) {
      throw Exception('Failed to load Evening Adhkar: $e');
    }
  }

  @override
  Future<List<DhikrModel>> getAfterPrayerAdhkar() async {
    try {
      final String response = await rootBundle.loadString('assets/data/adhkar.json');
      final List<dynamic> data = json.decode(response);
      return data
          .where((dhikr) => dhikr['category'] == 'after_prayer')
          .map((json) => DhikrModel.fromJson(json))
          .toList();
    } catch (e) {
      throw Exception('Failed to load After Prayer Adhkar: $e');
    }
  }

  @override
  Future<List<DhikrModel>> getBeforeSleepAdhkar() async {
    try {
      final String response = await rootBundle.loadString('assets/data/adhkar.json');
      final List<dynamic> data = json.decode(response);
      return data
          .where((dhikr) => dhikr['category'] == 'before_sleep')
          .map((json) => DhikrModel.fromJson(json))
          .toList();
    } catch (e) {
      throw Exception('Failed to load Before Sleep Adhkar: $e');
    }
  }

  @override
  Future<List<DhikrModel>> getAdhkarByCategory(String category) async {
    try {
      final String response = await rootBundle.loadString('assets/data/adhkar.json');
      final List<dynamic> data = json.decode(response);
      return data
          .where((dhikr) => dhikr['category'] == category)
          .map((json) => DhikrModel.fromJson(json))
          .toList();
    } catch (e) {
      throw Exception('Failed to load Adhkar by category: $e');
    }
  }

  @override
  Future<List<DhikrModel>> searchAdhkar(String query) async {
    try {
      final String response = await rootBundle.loadString('assets/data/adhkar.json');
      final List<dynamic> data = json.decode(response);
      return data
          .where((dhikr) =>
              dhikr['text'].toLowerCase().contains(query.toLowerCase()) ||
              dhikr['translation'].toLowerCase().contains(query.toLowerCase()))
          .map((json) => DhikrModel.fromJson(json))
          .toList();
    } catch (e) {
      throw Exception('Failed to search Adhkar: $e');
    }
  }
} 