import 'package:quran_flutter/features/adhkar/data/datasources/adhkar_local_data_source.dart';
import 'package:quran_flutter/features/adhkar/domain/entities/dhikr.dart';
import 'package:quran_flutter/features/adhkar/domain/repositories/adhkar_repository.dart';

class AdhkarRepositoryImpl implements AdhkarRepository {
  final AdhkarLocalDataSource localDataSource;

  AdhkarRepositoryImpl(this.localDataSource);

  @override
  Future<List<Dhikr>> getMorningAdhkar() async {
    try {
      final adhkar = await localDataSource.getMorningAdhkar();
      return adhkar;
    } catch (e) {
      throw Exception('Failed to get Morning Adhkar: $e');
    }
  }

  @override
  Future<List<Dhikr>> getEveningAdhkar() async {
    try {
      final adhkar = await localDataSource.getEveningAdhkar();
      return adhkar;
    } catch (e) {
      throw Exception('Failed to get Evening Adhkar: $e');
    }
  }

  @override
  Future<List<Dhikr>> getAfterPrayerAdhkar() async {
    try {
      final adhkar = await localDataSource.getAfterPrayerAdhkar();
      return adhkar;
    } catch (e) {
      throw Exception('Failed to get After Prayer Adhkar: $e');
    }
  }

  @override
  Future<List<Dhikr>> getBeforeSleepAdhkar() async {
    try {
      final adhkar = await localDataSource.getBeforeSleepAdhkar();
      return adhkar;
    } catch (e) {
      throw Exception('Failed to get Before Sleep Adhkar: $e');
    }
  }

  @override
  Future<List<Dhikr>> getAdhkarByCategory(String category) async {
    try {
      final adhkar = await localDataSource.getAdhkarByCategory(category);
      return adhkar;
    } catch (e) {
      throw Exception('Failed to get Adhkar by category: $e');
    }
  }

  @override
  Future<List<Dhikr>> searchAdhkar(String query) async {
    try {
      final adhkar = await localDataSource.searchAdhkar(query);
      return adhkar;
    } catch (e) {
      throw Exception('Failed to search Adhkar: $e');
    }
  }
} 