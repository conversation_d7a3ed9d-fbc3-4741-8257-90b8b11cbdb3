import 'package:flutter_bloc/flutter_bloc.dart';
import 'package:quran_flutter/features/adhkar/domain/usecases/get_morning_adhkar.dart';
import 'package:quran_flutter/features/adhkar/domain/usecases/get_evening_adhkar.dart';
import 'package:quran_flutter/features/adhkar/presentation/bloc/adhkar_event.dart';
import 'package:quran_flutter/features/adhkar/presentation/bloc/adhkar_state.dart';

class AdhkarBloc extends Bloc<AdhkarEvent, AdhkarState> {
  final GetMorningAdhkar getMorningAdhkar;
  final GetEveningAdhkar getEveningAdhkar;

  AdhkarBloc({
    required this.getMorningAdhkar,
    required this.getEveningAdhkar,
  }) : super(AdhkarInitial()) {
    on<LoadMorningAdhkar>(_onLoadMorningAdhkar);
    on<LoadEveningAdhkar>(_onLoadEveningAdhkar);
    on<LoadAfterPrayerAdhkar>(_onLoadAfterPrayerAdhkar);
    on<LoadBeforeSleepAdhkar>(_onLoadBeforeSleepAdhkar);
    on<LoadAdhkarByCategory>(_onLoadAdhkarByCategory);
    on<SearchAdhkar>(_onSearchAdhkar);
  }

  Future<void> _onLoadMorningAdhkar(
    LoadMorningAdhkar event,
    Emitter<AdhkarState> emit,
  ) async {
    emit(AdhkarLoading());
    try {
      final adhkar = await getMorningAdhkar();
      emit(AdhkarLoaded(adhkar: adhkar, category: 'morning'));
    } catch (e) {
      emit(AdhkarError(e.toString()));
    }
  }

  Future<void> _onLoadEveningAdhkar(
    LoadEveningAdhkar event,
    Emitter<AdhkarState> emit,
  ) async {
    emit(AdhkarLoading());
    try {
      final adhkar = await getEveningAdhkar();
      emit(AdhkarLoaded(adhkar: adhkar, category: 'evening'));
    } catch (e) {
      emit(AdhkarError(e.toString()));
    }
  }

  Future<void> _onLoadAfterPrayerAdhkar(
    LoadAfterPrayerAdhkar event,
    Emitter<AdhkarState> emit,
  ) async {
    emit(AdhkarLoading());
    try {
      // TODO: Implement after prayer adhkar
      emit(AdhkarLoaded(adhkar: [], category: 'after_prayer'));
    } catch (e) {
      emit(AdhkarError(e.toString()));
    }
  }

  Future<void> _onLoadBeforeSleepAdhkar(
    LoadBeforeSleepAdhkar event,
    Emitter<AdhkarState> emit,
  ) async {
    emit(AdhkarLoading());
    try {
      // TODO: Implement before sleep adhkar
      emit(AdhkarLoaded(adhkar: [], category: 'before_sleep'));
    } catch (e) {
      emit(AdhkarError(e.toString()));
    }
  }

  Future<void> _onLoadAdhkarByCategory(
    LoadAdhkarByCategory event,
    Emitter<AdhkarState> emit,
  ) async {
    emit(AdhkarLoading());
    try {
      // TODO: Implement adhkar by category
      emit(AdhkarLoaded(adhkar: [], category: event.category));
    } catch (e) {
      emit(AdhkarError(e.toString()));
    }
  }

  Future<void> _onSearchAdhkar(
    SearchAdhkar event,
    Emitter<AdhkarState> emit,
  ) async {
    emit(AdhkarLoading());
    try {
      // TODO: Implement adhkar search
      emit(AdhkarLoaded(adhkar: [], category: 'search'));
    } catch (e) {
      emit(AdhkarError(e.toString()));
    }
  }
} 