import 'package:flutter_bloc/flutter_bloc.dart';
import 'package:quran_flutter/features/quran/domain/usecases/get_all_surahs.dart';
import 'package:quran_flutter/features/quran/domain/usecases/get_ayahs_by_surah.dart';
import 'package:quran_flutter/features/quran/domain/usecases/search_ayahs.dart';
import 'package:quran_flutter/features/quran/presentation/bloc/quran_event.dart';
import 'package:quran_flutter/features/quran/presentation/bloc/quran_state.dart';

class QuranBloc extends Bloc<QuranEvent, QuranState> {
  final GetAllSurahs getAllSurahs;
  final GetAyahsBySurah getAyahsBySurah;
  final SearchAyahsUseCase searchAyahs;

  QuranBloc({
    required this.getAllSurahs,
    required this.getAyahsBySurah,
    required this.searchAyahs,
  }) : super(QuranInitial()) {
    on<LoadAllSurahs>(_onLoadAllSurahs);
    on<LoadAyahsBySurah>(_onLoadAyahsBySurah);
    on<SearchAyahs>(_onSearchAyahs);
  }

  Future<void> _onLoadAllSurahs(
    LoadAllSurahs event,
    Emitter<QuranState> emit,
  ) async {
    emit(QuranLoading());
    try {
      final surahs = await getAllSurahs();
      emit(QuranLoaded(surahs: surahs, currentView: 'surahs'));
    } catch (e) {
      emit(QuranError(e.toString()));
    }
  }

  Future<void> _onLoadAyahsBySurah(
    LoadAyahsBySurah event,
    Emitter<QuranState> emit,
  ) async {
    emit(QuranLoading());
    try {
      final surahs = await getAllSurahs();
      final ayahs = await getAyahsBySurah(event.surahNumber);
      emit(QuranLoaded(
        surahs: surahs,
        ayahs: ayahs,
        currentView: 'ayahs',
      ));
    } catch (e) {
      emit(QuranError(e.toString()));
    }
  }

  Future<void> _onSearchAyahs(
    SearchAyahs event,
    Emitter<QuranState> emit,
  ) async {
    emit(QuranLoading());
    try {
      final surahs = await getAllSurahs();
      final ayahs = await searchAyahs(event.query);
      emit(QuranLoaded(
        surahs: surahs,
        ayahs: ayahs,
        currentView: 'search',
      ));
    } catch (e) {
      emit(QuranError(e.toString()));
    }
  }
} 