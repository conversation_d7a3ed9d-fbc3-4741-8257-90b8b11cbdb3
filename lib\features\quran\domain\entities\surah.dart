
import 'package:equatable/equatable.dart';

class <PERSON><PERSON> extends Equatable {
  final int number;
  final String name;
  final String nameArabic;
  final String nameEnglish;
  final String revelationType;
  final int numberOfAyahs;
  final int juz;

  const <PERSON><PERSON>({
    required this.number,
    required this.name,
    required this.nameArabic,
    required this.nameEnglish,
    required this.revelationType,
    required this.numberOfAyahs,
    required this.juz,
  });

  @override
  List<Object?> get props => [
        number,
        name,
        nameArabic,
        nameEnglish,
        revelationType,
        numberOfAyahs,
        juz,
      ];
}
