import 'package:flutter/material.dart';
import 'package:flutter_bloc/flutter_bloc.dart';
import 'package:hive_flutter/hive_flutter.dart';
import 'package:just_audio_background/just_audio_background.dart';
import 'package:quran_flutter/features/adhkar/data/datasources/adhkar_local_data_source.dart';
import 'package:quran_flutter/features/adhkar/data/repositories/adhkar_repository_impl.dart';
import 'package:quran_flutter/features/adhkar/domain/usecases/get_morning_adhkar.dart';
import 'package:quran_flutter/features/adhkar/domain/usecases/get_evening_adhkar.dart';
import 'package:quran_flutter/features/adhkar/presentation/bloc/adhkar_bloc.dart';
import 'package:quran_flutter/features/home/<USER>/pages/main_page.dart';
import 'package:quran_flutter/features/quran/data/datasources/quran_local_data_source.dart';
import 'package:quran_flutter/features/quran/data/repositories/quran_repository_impl.dart';
import 'package:quran_flutter/features/quran/domain/usecases/get_all_surahs.dart';
import 'package:quran_flutter/features/quran/domain/usecases/get_ayahs_by_surah.dart';
import 'package:quran_flutter/features/quran/domain/usecases/search_ayahs.dart';
import 'package:quran_flutter/features/quran/presentation/bloc/quran_bloc.dart';
import 'package:quran_flutter/features/settings/presentation/bloc/settings_bloc.dart';
import 'package:quran_flutter/features/settings/presentation/bloc/settings_state.dart';

Future<void> main() async {
  WidgetsFlutterBinding.ensureInitialized();
  await Hive.initFlutter();
  await JustAudioBackground.init(
    androidNotificationChannelId: 'com.ryanheise.bg_demo.channel.audio',
    androidNotificationChannelName: 'Audio playback',
    androidNotificationOngoing: true,
  );
  runApp(const MyApp());
}

class MyApp extends StatelessWidget {
  const MyApp({super.key});

  @override
  Widget build(BuildContext context) {
    return MultiBlocProvider(
      providers: [
        BlocProvider<SettingsBloc>(
          create: (context) => SettingsBloc(),
        ),
        BlocProvider<QuranBloc>(
          create: (context) => QuranBloc(
            getAllSurahs: GetAllSurahs(
              QuranRepositoryImpl(
                QuranLocalDataSourceImpl(),
              ),
            ),
            getAyahsBySurah: GetAyahsBySurah(
              QuranRepositoryImpl(
                QuranLocalDataSourceImpl(),
              ),
            ),
            searchAyahs: SearchAyahsUseCase(
              QuranRepositoryImpl(
                QuranLocalDataSourceImpl(),
              ),
            ),
          ),
        ),
        BlocProvider<AdhkarBloc>(
          create: (context) => AdhkarBloc(
            getMorningAdhkar: GetMorningAdhkar(
              AdhkarRepositoryImpl(
                AdhkarLocalDataSourceImpl(),
              ),
            ),
            getEveningAdhkar: GetEveningAdhkar(
              AdhkarRepositoryImpl(
                AdhkarLocalDataSourceImpl(),
              ),
            ),
          ),
        ),
      ],
      child: BlocBuilder<SettingsBloc, SettingsState>(
        builder: (context, state) {
          return MaterialApp(
            title: 'Quran Flutter',
            theme: state.themeData,
            home: const MainPage(),
            debugShowCheckedModeBanner: false,
          );
        },
      ),
    );
  }
}