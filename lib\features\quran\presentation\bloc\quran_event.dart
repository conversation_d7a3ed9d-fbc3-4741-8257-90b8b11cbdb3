
import 'package:equatable/equatable.dart';

abstract class Quran<PERSON>vent extends Equatable {
  const QuranEvent();

  @override
  List<Object?> get props => [];
}

class LoadAllSurahs extends QuranEvent {}

class Load<PERSON>yahsBySurah extends QuranEvent {
  final int surahNumber;

  const LoadAyahsBySurah(this.surahNumber);

  @override
  List<Object?> get props => [surahNumber];
}

class SearchAyahs extends QuranEvent {
  final String query;

  const SearchAyahs(this.query);

  @override
  List<Object?> get props => [query];
}

class LoadAyahsByJuz extends QuranEvent {
  final int juz;

  const LoadAyahsByJuz(this.juz);

  @override
  List<Object?> get props => [juz];
}

class LoadAyahsByPage extends QuranEvent {
  final int page;

  const LoadAyahsByPage(this.page);

  @override
  List<Object?> get props => [page];
}
