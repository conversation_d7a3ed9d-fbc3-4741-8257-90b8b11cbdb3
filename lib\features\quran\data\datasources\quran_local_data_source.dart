
import 'dart:convert';

import 'package:flutter/services.dart';
import 'package:quran_flutter/features/quran/data/models/ayah_model.dart';
import 'package:quran_flutter/features/quran/data/models/surah_model.dart';

abstract class QuranLocalDataSource {
  Future<List<SurahModel>> getAllSurahs();
  Future<List<AyahModel>> getAyahsBySurah(int surahNumber);
  Future<List<AyahModel>> searchAyahs(String query);
  Future<AyahModel> getAyahByNumber(int ayahNumber);
  Future<List<AyahModel>> getAyahsByJuz(int juz);
  Future<List<AyahModel>> getAyahsByPage(int page);
}

class QuranLocalDataSourceImpl implements QuranLocalDataSource {
  @override
  Future<List<SurahModel>> getAllSurahs() async {
    try {
      final String response = await rootBundle.loadString('assets/data/quran.json');
      final List<dynamic> data = json.decode(response);
      return data.map((json) => SurahModel.fromJson(json)).toList();
    } catch (e) {
      throw Exception('Failed to load Surahs: $e');
    }
  }

  @override
  Future<List<AyahModel>> getAyahsBySurah(int surahNumber) async {
    try {
      final String response = await rootBundle.loadString('assets/data/quran.json');
      final List<dynamic> data = json.decode(response);
      
      // Find the surah and return its ayahs
      for (var surah in data) {
        if (surah['number'] == surahNumber && surah['ayahs'] != null) {
          return (surah['ayahs'] as List)
              .map((json) => AyahModel.fromJson(json))
              .toList();
        }
      }
      return [];
    } catch (e) {
      throw Exception('Failed to load Ayahs: $e');
    }
  }

  @override
  Future<List<AyahModel>> searchAyahs(String query) async {
    try {
      final String response = await rootBundle.loadString('assets/data/quran.json');
      final List<dynamic> data = json.decode(response);
      
      List<AyahModel> results = [];
      for (var surah in data) {
        if (surah['ayahs'] != null) {
          for (var ayah in surah['ayahs']) {
            if (ayah['text'].toLowerCase().contains(query.toLowerCase()) ||
                ayah['translation'].toLowerCase().contains(query.toLowerCase())) {
              results.add(AyahModel.fromJson(ayah));
            }
          }
        }
      }
      return results;
    } catch (e) {
      throw Exception('Failed to search Ayahs: $e');
    }
  }

  @override
  Future<AyahModel> getAyahByNumber(int ayahNumber) async {
    try {
      final String response = await rootBundle.loadString('assets/data/quran.json');
      final List<dynamic> data = json.decode(response);
      
      for (var surah in data) {
        if (surah['ayahs'] != null) {
          for (var ayah in surah['ayahs']) {
            if (ayah['number'] == ayahNumber) {
              return AyahModel.fromJson(ayah);
            }
          }
        }
      }
      throw Exception('Ayah not found');
    } catch (e) {
      throw Exception('Failed to get Ayah: $e');
    }
  }

  @override
  Future<List<AyahModel>> getAyahsByJuz(int juz) async {
    try {
      final String response = await rootBundle.loadString('assets/data/quran.json');
      final List<dynamic> data = json.decode(response);
      
      List<AyahModel> results = [];
      for (var surah in data) {
        if (surah['ayahs'] != null) {
          for (var ayah in surah['ayahs']) {
            if (ayah['juz'] == juz) {
              results.add(AyahModel.fromJson(ayah));
            }
          }
        }
      }
      return results;
    } catch (e) {
      throw Exception('Failed to get Ayahs by Juz: $e');
    }
  }

  @override
  Future<List<AyahModel>> getAyahsByPage(int page) async {
    try {
      final String response = await rootBundle.loadString('assets/data/quran.json');
      final List<dynamic> data = json.decode(response);
      
      List<AyahModel> results = [];
      for (var surah in data) {
        if (surah['ayahs'] != null) {
          for (var ayah in surah['ayahs']) {
            if (ayah['page'] == page) {
              results.add(AyahModel.fromJson(ayah));
            }
          }
        }
      }
      return results;
    } catch (e) {
      throw Exception('Failed to get Ayahs by Page: $e');
    }
  }
}
