import 'package:equatable/equatable.dart';
import 'package:quran_flutter/features/adhkar/domain/entities/dhikr.dart';

abstract class AdhkarState extends Equatable {
  const AdhkarState();

  @override
  List<Object?> get props => [];
}

class AdhkarInitial extends AdhkarState {}

class AdhkarLoading extends AdhkarState {}

class AdhkarLoaded extends AdhkarState {
  final List<Dhikr> adhkar;
  final String category;

  const AdhkarLoaded({
    required this.adhkar,
    required this.category,
  });

  @override
  List<Object?> get props => [adhkar, category];
}

class AdhkarError extends AdhkarState {
  final String message;

  const AdhkarError(this.message);

  @override
  List<Object?> get props => [message];
} 