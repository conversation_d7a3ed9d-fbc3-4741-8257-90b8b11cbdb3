# Quran Flutter

A beautiful and comprehensive Flutter mobile application for the Holy Quran and Islamic Adhkar, built with clean architecture and BLoC state management.

## Features

### 🕌 Quran Features
- **Complete Quran Text**: Full Quran in Uthmani script with English translations
- **Surah Navigation**: Easy navigation through all 114 surahs
- **Search Functionality**: Search by surah name or verse keywords
- **Audio Recitation**: Built-in audio player for Quran recitations
- **Bookmark System**: Save and organize your favorite verses
- **Juz & Page Navigation**: Navigate by Juz or page numbers

### 📿 Adhkar Features
- **Morning Adhkar**: Complete collection of morning supplications
- **Evening Adhkar**: Evening supplications and remembrances
- **After Prayer Adhkar**: Supplications to recite after prayers
- **Before Sleep Adhkar**: Bedtime supplications
- **Counter System**: Track your dhikr counts with increment/decrement/reset
- **Category Organization**: Organized by time and purpose

### 🎨 UI/UX Features
- **Modern Design**: Beautiful, minimal, and responsive interface
- **Light & Dark Themes**: Switch between light and dark modes
- **RTL Support**: Full Arabic text support with right-to-left layout
- **Responsive Design**: Optimized for both mobile and tablet
- **Smooth Animations**: Elegant transitions and micro-interactions

### ⚙️ Settings & Customization
- **Theme Switching**: Light and dark mode toggle
- **Font Size Control**: Adjustable text size for better readability
- **Reciter Selection**: Choose from multiple audio reciters
- **App Preferences**: Personalized settings and configurations

## Architecture

The application follows **Clean Architecture** principles with **BLoC** state management:

```
lib/
├── core/                    # Core functionality
│   ├── constants/          # App constants and colors
│   ├── theme/             # App themes and styling
│   └── utils/             # Utility functions
├── features/               # Feature modules
│   ├── quran/             # Quran feature
│   │   ├── data/          # Data layer (models, datasources, repositories)
│   │   ├── domain/        # Domain layer (entities, usecases, repositories)
│   │   └── presentation/  # Presentation layer (BLoC, pages, widgets)
│   ├── adhkar/            # Adhkar feature
│   ├── bookmarks/         # Bookmarks feature
│   ├── settings/          # Settings feature
│   └── home/              # Home feature
└── main.dart              # App entry point
```

## Technology Stack

- **Flutter**: Cross-platform mobile development framework
- **BLoC**: State management using flutter_bloc
- **Hive**: Local data storage and caching
- **Just Audio**: Audio playback for Quran recitations
- **Google Fonts**: Typography and font management
- **Material Design 3**: Modern UI components and design system

## Getting Started

### Prerequisites
- Flutter SDK (3.8.1 or higher)
- Dart SDK
- Android Studio / VS Code
- Android SDK / Xcode (for iOS)

### Installation

1. **Clone the repository**
   ```bash
   git clone https://github.com/yourusername/quran_flutter.git
   cd quran_flutter
   ```

2. **Install dependencies**
   ```bash
   flutter pub get
   ```

3. **Run the application**
   ```bash
   flutter run
   ```

### Build for Production

**Android APK:**
```bash
flutter build apk --release
```

**iOS IPA:**
```bash
flutter build ios --release
```

## Data Structure

### Quran Data Format
```json
{
  "number": 1,
  "name": "Al-Fatiha",
  "nameArabic": "الفاتحة",
  "nameEnglish": "The Opening",
  "revelationType": "Meccan",
  "numberOfAyahs": 7,
  "juz": 1,
  "ayahs": [...]
}
```

### Adhkar Data Format
```json
{
  "id": "morning_1",
  "text": "أَصْبَحْنَا وَأَصْبَحَ الْمُلْكُ لِلَّهِ...",
  "translation": "We have reached the morning...",
  "category": "morning",
  "recommendedCount": 1,
  "time": "Morning",
  "description": "Morning dhikr to be recited once"
}
```

## Contributing

We welcome contributions! Please feel free to submit issues, feature requests, or pull requests.

### Development Guidelines
- Follow Flutter best practices and conventions
- Maintain clean architecture principles
- Write comprehensive tests for new features
- Ensure code follows the established style guide
- Update documentation for any API changes

## License

This project is licensed under the MIT License - see the [LICENSE](LICENSE) file for details.

## Acknowledgments

- Quran text and translations from reliable Islamic sources
- Adhkar content from authentic Islamic literature
- Flutter community for excellent packages and support
- Material Design team for design inspiration

## Support

If you have any questions or need support, please:
- Check the [Issues](https://github.com/yourusername/quran_flutter/issues) page
- Create a new issue for bugs or feature requests
- Contact the development team

---

**Made with ❤️ for the Muslim Ummah**
