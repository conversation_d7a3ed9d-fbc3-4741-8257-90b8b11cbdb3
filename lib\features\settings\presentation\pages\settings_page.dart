
import 'package:flutter/material.dart';
import 'package:flutter_bloc/flutter_bloc.dart';
import 'package:quran_flutter/core/constants/colors.dart';
import 'package:quran_flutter/features/settings/presentation/bloc/settings_bloc.dart';
import 'package:quran_flutter/features/settings/presentation/bloc/settings_event.dart';
import 'package:quran_flutter/features/settings/presentation/bloc/settings_state.dart';

class SettingsPage extends StatefulWidget {
  const SettingsPage({super.key});

  @override
  State<SettingsPage> createState() => _SettingsPageState();
}

class _SettingsPageState extends State<SettingsPage> {
  double _fontSize = 16.0;
  String _selectedReciter = '<PERSON>';

  final List<String> _reciters = [
    '<PERSON>',
    '<PERSON><PERSON>',
    '<PERSON><PERSON><PERSON>',
    '<PERSON>',
    '<PERSON><PERSON>',
  ];

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      appBar: AppBar(
        title: const Text('Settings'),
        backgroundColor: Colors.orange,
        foregroundColor: Colors.white,
        elevation: 0,
      ),
      body: BlocBuilder<SettingsBloc, SettingsState>(
        builder: (context, state) {
          return ListView(
            padding: const EdgeInsets.all(16),
            children: [
              // Theme Section
              _buildSectionHeader('Appearance'),
              _buildThemeSwitch(state),
              const SizedBox(height: 24),
              
              // Font Settings
              _buildSectionHeader('Text Settings'),
              _buildFontSizeSlider(),
              const SizedBox(height: 24),
              
              // Audio Settings
              _buildSectionHeader('Audio Settings'),
              _buildReciterDropdown(),
              const SizedBox(height: 24),
              
              // App Info
              _buildSectionHeader('App Information'),
              _buildAppInfo(),
            ],
          );
        },
      ),
    );
  }

  Widget _buildSectionHeader(String title) {
    return Padding(
      padding: const EdgeInsets.only(bottom: 12),
      child: Text(
        title,
        style: const TextStyle(
          fontSize: 18,
          fontWeight: FontWeight.bold,
          color: AppColors.primary,
        ),
      ),
    );
  }

  Widget _buildThemeSwitch(SettingsState state) {
    return Card(
      elevation: 2,
      shape: RoundedRectangleBorder(
        borderRadius: BorderRadius.circular(12),
      ),
      child: SwitchListTile(
        title: const Text('Dark Mode'),
        subtitle: const Text('Switch between light and dark themes'),
        value: state.themeData.brightness == Brightness.dark,
        onChanged: (value) {
          context.read<SettingsBloc>().add(
            ThemeChanged(isDarkMode: value),
          );
        },
        secondary: Icon(
          state.themeData.brightness == Brightness.dark
              ? Icons.dark_mode
              : Icons.light_mode,
          color: AppColors.primary,
        ),
      ),
    );
  }

  Widget _buildFontSizeSlider() {
    return Card(
      elevation: 2,
      shape: RoundedRectangleBorder(
        borderRadius: BorderRadius.circular(12),
      ),
      child: Padding(
        padding: const EdgeInsets.all(16),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            Row(
              children: [
                const Icon(Icons.text_fields, color: AppColors.primary),
                const SizedBox(width: 12),
                const Text(
                  'Font Size',
                  style: TextStyle(
                    fontSize: 16,
                    fontWeight: FontWeight.w500,
                  ),
                ),
                const Spacer(),
                Text(
                  '${_fontSize.round()}',
                  style: TextStyle(
                    fontSize: 16,
                    fontWeight: FontWeight.bold,
                    color: AppColors.primary,
                  ),
                ),
              ],
            ),
            const SizedBox(height: 16),
            Slider(
              value: _fontSize,
              min: 12.0,
              max: 24.0,
              divisions: 12,
              activeColor: AppColors.primary,
              onChanged: (value) {
                setState(() {
                  _fontSize = value;
                });
              },
            ),
            Row(
              mainAxisAlignment: MainAxisAlignment.spaceBetween,
              children: [
                Text(
                  'Small',
                  style: TextStyle(
                    fontSize: 12,
                    color: Colors.grey[600],
                  ),
                ),
                Text(
                  'Large',
                  style: TextStyle(
                    fontSize: 12,
                    color: Colors.grey[600],
                  ),
                ),
              ],
            ),
          ],
        ),
      ),
    );
  }

  Widget _buildReciterDropdown() {
    return Card(
      elevation: 2,
      shape: RoundedRectangleBorder(
        borderRadius: BorderRadius.circular(12),
      ),
      child: Padding(
        padding: const EdgeInsets.all(16),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            Row(
              children: [
                const Icon(Icons.volume_up, color: AppColors.primary),
                const SizedBox(width: 12),
                const Text(
                  'Audio Reciter',
                  style: TextStyle(
                    fontSize: 16,
                    fontWeight: FontWeight.w500,
                  ),
                ),
              ],
            ),
            const SizedBox(height: 16),
            DropdownButtonFormField<String>(
              value: _selectedReciter,
              decoration: InputDecoration(
                border: OutlineInputBorder(
                  borderRadius: BorderRadius.circular(8),
                ),
                contentPadding: const EdgeInsets.symmetric(
                  horizontal: 12,
                  vertical: 8,
                ),
              ),
              items: _reciters.map((String reciter) {
                return DropdownMenuItem<String>(
                  value: reciter,
                  child: Text(reciter),
                );
              }).toList(),
              onChanged: (String? newValue) {
                if (newValue != null) {
                  setState(() {
                    _selectedReciter = newValue;
                  });
                }
              },
            ),
          ],
        ),
      ),
    );
  }

  Widget _buildAppInfo() {
    return Card(
      elevation: 2,
      shape: RoundedRectangleBorder(
        borderRadius: BorderRadius.circular(12),
      ),
      child: Padding(
        padding: const EdgeInsets.all(16),
        child: Column(
          children: [
            ListTile(
              leading: const Icon(Icons.info_outline, color: AppColors.primary),
              title: const Text('App Version'),
              subtitle: const Text('1.0.0'),
            ),
            const Divider(),
            ListTile(
              leading: const Icon(Icons.description, color: AppColors.primary),
              title: const Text('About'),
              subtitle: const Text('Quran Flutter - Holy Quran & Islamic Adhkar'),
              onTap: () {
                _showAboutDialog();
              },
            ),
            const Divider(),
            ListTile(
              leading: const Icon(Icons.feedback, color: AppColors.primary),
              title: const Text('Feedback'),
              subtitle: const Text('Send us your feedback'),
              onTap: () {
                // TODO: Implement feedback functionality
              },
            ),
          ],
        ),
      ),
    );
  }

  void _showAboutDialog() {
    showAboutDialog(
      context: context,
      applicationName: 'Quran Flutter',
      applicationVersion: '1.0.0',
      applicationIcon: const Icon(
        Icons.menu_book,
        size: 48,
        color: AppColors.primary,
      ),
      children: [
        const Text(
          'A beautiful and comprehensive Quran and Adhkar application built with Flutter.',
        ),
        const SizedBox(height: 16),
        const Text(
          'Features:\n• Complete Quran text with translations\n• Morning and Evening Adhkar\n• Audio recitations\n• Bookmarks and search\n• Light and Dark themes',
        ),
      ],
    );
  }
}
