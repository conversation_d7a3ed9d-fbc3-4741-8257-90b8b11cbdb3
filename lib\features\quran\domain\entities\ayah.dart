
import 'package:equatable/equatable.dart';

class Ayah extends Equatable {
  final int number;
  final int surahNumber;
  final String text;
  final String translation;
  final int juz;
  final int page;
  final int ruku;
  final int sajda;

  const Ayah({
    required this.number,
    required this.surah<PERSON>umber,
    required this.text,
    required this.translation,
    required this.juz,
    required this.page,
    required this.ruku,
    required this.sajda,
  });

  @override
  List<Object?> get props => [
        number,
        surahNumber,
        text,
        translation,
        juz,
        page,
        ruku,
        sajda,
      ];
}
