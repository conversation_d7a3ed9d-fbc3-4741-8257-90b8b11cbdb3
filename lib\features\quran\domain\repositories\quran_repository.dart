
import 'package:quran_flutter/features/quran/domain/entities/ayah.dart';
import 'package:quran_flutter/features/quran/domain/entities/surah.dart';

abstract class QuranRepository {
  Future<List<Surah>> getAllSurahs();
  Future<List<Ayah>> getAyahsBySurah(int surahNumber);
  Future<List<Ayah>> searchAyahs(String query);
  Future<Ayah> getAyahByNumber(int ayahNumber);
  Future<List<Ayah>> getAyahsByJuz(int juz);
  Future<List<Ayah>> getAyahsByPage(int page);
}
