import 'package:equatable/equatable.dart';

class Dhikr extends Equatable {
  final String id;
  final String text;
  final String translation;
  final String category;
  final int recommendedCount;
  final String time;
  final String description;

  const Dhikr({
    required this.id,
    required this.text,
    required this.translation,
    required this.category,
    required this.recommendedCount,
    required this.time,
    required this.description,
  });

  @override
  List<Object?> get props => [
        id,
        text,
        translation,
        category,
        recommendedCount,
        time,
        description,
      ];
} 