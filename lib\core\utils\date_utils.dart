import 'package:intl/intl.dart';

class AppDateUtils {
  static String getCurrentDate() {
    final now = DateTime.now();
    return DateFormat('EEEE, MMMM d, y').format(now);
  }

  static String getCurrentTime() {
    final now = DateTime.now();
    return DateFormat('HH:mm').format(now);
  }

  static bool isMorning() {
    final now = DateTime.now();
    return now.hour >= 5 && now.hour < 12;
  }

  static bool isEvening() {
    final now = DateTime.now();
    return now.hour >= 17 || now.hour < 5;
  }

  static bool isAfterPrayer() {
    final now = DateTime.now();
    // This is a simplified logic, you might want to implement proper prayer time calculation
    return now.hour >= 6 && now.hour < 22;
  }

  static bool isBeforeSleep() {
    final now = DateTime.now();
    return now.hour >= 21 || now.hour < 6;
  }
} 