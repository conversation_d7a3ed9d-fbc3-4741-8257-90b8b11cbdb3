import 'package:equatable/equatable.dart';

abstract class AdhkarEvent extends Equatable {
  const AdhkarEvent();

  @override
  List<Object?> get props => [];
}

class LoadMorningAdhkar extends AdhkarEvent {}

class LoadEveningAdhkar extends AdhkarEvent {}

class LoadAfterPrayerAdhkar extends AdhkarEvent {}

class LoadBeforeSleepAdhkar extends AdhkarEvent {}

class LoadAdhkarByCategory extends AdhkarEvent {
  final String category;

  const LoadAdhkarByCategory(this.category);

  @override
  List<Object?> get props => [category];
}

class SearchAdhkar extends AdhkarEvent {
  final String query;

  const SearchAdhkar(this.query);

  @override
  List<Object?> get props => [query];
} 