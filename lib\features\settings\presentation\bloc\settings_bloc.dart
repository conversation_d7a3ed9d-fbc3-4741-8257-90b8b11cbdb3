
import 'package:flutter_bloc/flutter_bloc.dart';
import 'package:quran_flutter/core/theme/app_theme.dart';
import 'package:quran_flutter/features/settings/presentation/bloc/settings_event.dart';
import 'package:quran_flutter/features/settings/presentation/bloc/settings_state.dart';

class SettingsBloc extends Bloc<SettingsEvent, SettingsState> {
  SettingsBloc() : super(SettingsState(themeData: AppTheme.lightTheme)) {
    on<ThemeChanged>((event, emit) {
      emit(SettingsState(themeData: event.isDarkMode ? AppTheme.darkTheme : AppTheme.lightTheme));
    });
  }
}
