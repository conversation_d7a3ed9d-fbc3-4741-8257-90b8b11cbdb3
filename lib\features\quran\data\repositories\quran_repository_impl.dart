
import 'package:quran_flutter/features/quran/data/datasources/quran_local_data_source.dart';
import 'package:quran_flutter/features/quran/domain/entities/ayah.dart';
import 'package:quran_flutter/features/quran/domain/entities/surah.dart';
import 'package:quran_flutter/features/quran/domain/repositories/quran_repository.dart';

class QuranRepositoryImpl implements QuranRepository {
  final QuranLocalDataSource localDataSource;

  QuranRepositoryImpl(this.localDataSource);

  @override
  Future<List<Surah>> getAllSurahs() async {
    try {
      final surahs = await localDataSource.getAllSurahs();
      return surahs;
    } catch (e) {
      throw Exception('Failed to get Surahs: $e');
    }
  }

  @override
  Future<List<Ayah>> getAyahsBySurah(int surahNumber) async {
    try {
      final ayahs = await localDataSource.getAyahsBySurah(surahNumber);
      return ayahs;
    } catch (e) {
      throw Exception('Failed to get Ayahs by Surah: $e');
    }
  }

  @override
  Future<List<Ayah>> searchAyahs(String query) async {
    try {
      final ayahs = await localDataSource.searchAyahs(query);
      return ayahs;
    } catch (e) {
      throw Exception('Failed to search Ayahs: $e');
    }
  }

  @override
  Future<Ayah> getAyahByNumber(int ayahNumber) async {
    try {
      final ayah = await localDataSource.getAyahByNumber(ayahNumber);
      return ayah;
    } catch (e) {
      throw Exception('Failed to get Ayah by number: $e');
    }
  }

  @override
  Future<List<Ayah>> getAyahsByJuz(int juz) async {
    try {
      final ayahs = await localDataSource.getAyahsByJuz(juz);
      return ayahs;
    } catch (e) {
      throw Exception('Failed to get Ayahs by Juz: $e');
    }
  }

  @override
  Future<List<Ayah>> getAyahsByPage(int page) async {
    try {
      final ayahs = await localDataSource.getAyahsByPage(page);
      return ayahs;
    } catch (e) {
      throw Exception('Failed to get Ayahs by Page: $e');
    }
  }
}
