import 'package:quran_flutter/features/adhkar/domain/entities/dhikr.dart';

class DhikrModel extends Dhikr {
  const DhikrModel({
    required super.id,
    required super.text,
    required super.translation,
    required super.category,
    required super.recommendedCount,
    required super.time,
    required super.description,
  });

  factory DhikrModel.fromJson(Map<String, dynamic> json) {
    return DhikrModel(
      id: json['id'] as String,
      text: json['text'] as String,
      translation: json['translation'] as String,
      category: json['category'] as String,
      recommendedCount: json['recommendedCount'] as int,
      time: json['time'] as String,
      description: json['description'] as String,
    );
  }

  Map<String, dynamic> toJson() {
    return {
      'id': id,
      'text': text,
      'translation': translation,
      'category': category,
      'recommendedCount': recommendedCount,
      'time': time,
      'description': description,
    };
  }
} 